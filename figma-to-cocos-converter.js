/**
 * Figma to Cocos Creator 转换器
 * 将Figma设计稿数据转换为Cocos Creator节点创建指令
 */

class FigmaToCocosConverter {
  constructor() {
    this.canvasWidth = 720;
    this.canvasHeight = 1280;
    this.nodeCommands = [];
  }

  // 坐标系转换：Figma左上角原点 -> Cocos中心原点
  convertCoordinates(figmaX, figmaY) {
    return {
      x: figmaX - this.canvasWidth / 2,
      y: this.canvasHeight / 2 - figmaY
    };
  }

  // 颜色转换：Figma 0-1范围 -> Cocos 0-255范围
  convertColor(figmaColor) {
    return {
      r: Math.round(figmaColor.r * 255),
      g: Math.round(figmaColor.g * 255),
      b: Math.round(figmaColor.b * 255),
      a: Math.round(figmaColor.a * 255)
    };
  }

  // 组件类型映射
  getComponentTypes(figmaNode) {
    const components = ["cc.UITransform"];
    
    switch (figmaNode.type) {
      case "RECTANGLE":
        components.push("cc.Sprite");
        // 如果名称包含Button，添加按钮组件
        if (figmaNode.name.toLowerCase().includes('button')) {
          components.push("cc.Button");
        }
        break;
      case "TEXT":
        components.push("cc.Label");
        break;
      case "FRAME":
        // Frame通常只需要UITransform
        break;
    }
    
    return components;
  }

  // 生成节点创建命令
  generateNodeCommand(figmaNode, parentUuid = null) {
    const pos = this.convertCoordinates(figmaNode.x, figmaNode.y);
    const components = this.getComponentTypes(figmaNode);
    
    const command = {
      action: "create_node",
      name: figmaNode.name,
      parentUuid: parentUuid,
      components: components,
      position: pos,
      size: {
        width: figmaNode.width,
        height: figmaNode.height
      },
      figmaData: figmaNode
    };

    return command;
  }

  // 生成属性设置命令
  generatePropertyCommands(figmaNode, nodeUuid) {
    const commands = [];

    // 设置尺寸
    commands.push({
      action: "set_transform",
      nodeUuid: nodeUuid,
      contentSize: {
        width: figmaNode.width,
        height: figmaNode.height
      }
    });

    // 设置颜色（如果有填充）
    if (figmaNode.fills && figmaNode.fills.length > 0) {
      const fill = figmaNode.fills[0];
      if (fill.type === "SOLID") {
        const color = this.convertColor(fill.color);
        
        if (figmaNode.type === "RECTANGLE") {
          commands.push({
            action: "set_sprite_color",
            nodeUuid: nodeUuid,
            color: color
          });
        } else if (figmaNode.type === "TEXT") {
          commands.push({
            action: "set_label_color",
            nodeUuid: nodeUuid,
            color: color
          });
        }
      }
    }

    // 设置文本属性
    if (figmaNode.type === "TEXT") {
      commands.push({
        action: "set_label_text",
        nodeUuid: nodeUuid,
        text: figmaNode.text,
        fontSize: figmaNode.fontSize || 24
      });
    }

    return commands;
  }

  // 递归转换节点树
  convertNode(figmaNode, parentUuid = null) {
    const nodeCommand = this.generateNodeCommand(figmaNode, parentUuid);
    this.nodeCommands.push(nodeCommand);
    
    // 模拟生成的UUID（实际使用时会从Cocos返回）
    const nodeUuid = `generated-uuid-${Date.now()}-${Math.random()}`;
    
    // 生成属性设置命令
    const propertyCommands = this.generatePropertyCommands(figmaNode, nodeUuid);
    this.nodeCommands.push(...propertyCommands);

    // 递归处理子节点
    if (figmaNode.children) {
      figmaNode.children.forEach(child => {
        this.convertNode(child, nodeUuid);
      });
    }

    return nodeUuid;
  }

  // 主转换方法
  convert(figmaData) {
    this.nodeCommands = [];
    this.convertNode(figmaData);
    return this.nodeCommands;
  }

  // 生成Cocos MCP调用序列
  generateMCPCalls(figmaData) {
    const commands = this.convert(figmaData);
    const mcpCalls = [];

    commands.forEach(cmd => {
      switch (cmd.action) {
        case "create_node":
          mcpCalls.push({
            tool: "node_node_lifecycle_cocos-creator",
            parameters: {
              action: "create",
              name: cmd.name,
              parentUuid: cmd.parentUuid,
              components: cmd.components,
              initialTransform: {
                position: cmd.position,
                scale: { x: 1, y: 1, z: 1 }
              }
            }
          });
          break;
        
        case "set_transform":
          mcpCalls.push({
            tool: "component_set_component_property_cocos-creator",
            parameters: {
              nodeUuid: cmd.nodeUuid,
              componentType: "cc.UITransform",
              properties: {
                contentSize: {
                  type: "size",
                  value: cmd.contentSize
                }
              }
            }
          });
          break;

        case "set_sprite_color":
          mcpCalls.push({
            tool: "component_set_component_property_cocos-creator",
            parameters: {
              nodeUuid: cmd.nodeUuid,
              componentType: "cc.Sprite",
              properties: {
                spriteFrame: {
                  type: "spriteFrame",
                  value: "57520716-48c8-4a19-8acf-41c9f8777fb0@f9941"
                },
                color: {
                  type: "color",
                  value: cmd.color
                }
              }
            }
          });
          break;

        case "set_label_text":
          mcpCalls.push({
            tool: "component_set_component_property_cocos-creator",
            parameters: {
              nodeUuid: cmd.nodeUuid,
              componentType: "cc.Label",
              properties: {
                string: {
                  type: "string",
                  value: cmd.text
                },
                fontSize: {
                  type: "number",
                  value: cmd.fontSize
                },
                color: {
                  type: "color",
                  value: cmd.color || { r: 255, g: 255, b: 255, a: 255 }
                }
              }
            }
          });
          break;
      }
    });

    return mcpCalls;
  }
}

// 导出转换器
if (typeof module !== 'undefined' && module.exports) {
  module.exports = FigmaToCocosConverter;
}
